---
// On définit les props attendues avec une interface TypeScript.
// C'est parfait pour la sécurité de typage dans votre éditeur, sans dépendance.
export interface Props {
  title: string;
  description: string;
  lang: "fr" | "en";
  image?: string; // L'image est optionnelle
}

// On récupère les props et on définit une image par défaut si aucune n'est fournie
const { 
  title, 
  description, 
  lang,
  image = "/images/social-default.jpg" // Changez pour votre image par défaut
} = Astro.props;

// Le nom de votre site, pour l'ajouter au titre des pages
const siteName = "<PERSON> | Développeur";
const fullTitle = `${title} | ${siteName}`;

// On construit les URLs absolues (indispensable pour le SEO et le partage)
// Note : Nécessite que `site` soit défini dans astro.config.mjs
const canonicalURL = new URL(Astro.url.pathname, Astro.site);
const imageURL = new URL(image, Astro.site);

// Un petit objet pour mapper la langue au format "locale" (fr -> fr_FR)
const locales = {
  fr: "fr_FR",
  en: "en_US",
};
---

<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>{fullTitle}</title>
<meta name="description" content={description} />

<meta name="robots" content="index, follow" />
<link rel="canonical" href={canonicalURL.href} />

<link rel="alternate" hreflang="fr" href={new URL(Astro.url.pathname.replace(/^\/[a-z]{2}/, '/fr'), Astro.site).href} />
<link rel="alternate" hreflang="en" href={new URL(Astro.url.pathname.replace(/^\/[a-z]{2}/, '/en'), Astro.site).href} />
<link rel="alternate" hreflang="x-default" href={new URL(Astro.url.pathname.replace(/^\/[a-z]{2}/, '/fr'), Astro.site).href} />

<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:type" content="website" />
<meta property="og:url" content={canonicalURL.href} />
<meta property="og:image" content={imageURL.href} />
<meta property="og:locale" content={locales[lang]} />
<meta property="og:site_name" content={siteName} />

<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content={title} />
<meta name="twitter:description" content={description} />
<meta name="twitter:image" content={imageURL.href} />

<link rel="icon" type="image/svg+xml" href="/favicon.ico" />