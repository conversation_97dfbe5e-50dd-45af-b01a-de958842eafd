---
import Icon from "./Icon.astro";
---

<!-- Theming -->
<script is:inline>
  const getThemePreference = () => {
    if (typeof localStorage !== "undefined" && localStorage.getItem("theme")) {
      return localStorage.getItem("theme");
    }
    return window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light";
  };
  const isDark = getThemePreference() === "dark";
  document.documentElement.classList.toggle("theme-dark", isDark);

  if (typeof localStorage !== "undefined") {
    const observer = new MutationObserver(() => {
      const isDark = document.documentElement.classList.contains("theme-dark");
      localStorage.setItem("theme", isDark ? "dark" : "light");
    });
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["class"],
    });
  }
</script>

<theme-toggle>
  <button>
    <span class="sr-only">Dark theme</span>
    <span class="icon light"><Icon icon="sun" /></span>
    <span class="icon dark"><Icon icon="moon" /></span>
  </button>
</theme-toggle>

<style>
  .theme-toggle {
    color: var(--gray-100); /* ou var(--accent-regular), etc. */
  }

  button {
    display: flex;
    border: 0;
    border-radius: 999rem;
    padding: 0;
    background-color: var(--gray-999);
    box-shadow: inset 0 0 0 1px var(--accent-overlay);
    cursor: pointer;
  }

  .icon {
    z-index: 1;
    position: relative;
    display: flex;
    padding: 0.5rem;
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
    color: var(--accent-overlay);
  }

  .icon.light::before {
    content: "";
    z-index: -1;
    position: absolute;
    inset: 0;
    background-color: var(--accent-regular);
    border-radius: 999rem;
  }

  :global(.theme-dark) .icon.light::before {
    transform: translateX(100%);
  }

  :global(.theme-dark) .icon.dark,
  :global(html:not(.theme-dark)) .icon.light,
  button[aria-pressed="false"] .icon.light {
    color: var(--accent-text-over);
  }

  @media (prefers-reduced-motion: no-preference) {
    .icon,
    .icon.light::before {
      transition:
        transform var(--theme-transition),
        color var(--theme-transition);
    }
  }

  @media (forced-colors: active) {
    .icon.light::before {
      background-color: SelectedItem;
    }
  }
</style>

<script>
  class ThemeToggle extends HTMLElement {
    constructor() {
      super();

      const button = this.querySelector("button")!;

      /** Set the theme to dark/light mode. */
      const setTheme = (dark: boolean) => {
        document.documentElement.classList[dark ? "add" : "remove"](
          "theme-dark"
        );
        button.setAttribute("aria-pressed", String(dark));
      };

      // Toggle the theme when a user clicks the button.
      button.addEventListener("click", () => setTheme(!this.isDark()));

      // // Initialize button state to reflect current theme.
      setTheme(this.isDark());
    }

    isDark() {
      return document.documentElement.classList.contains("theme-dark");
    }
  }
  customElements.define("theme-toggle", ThemeToggle);
</script>
