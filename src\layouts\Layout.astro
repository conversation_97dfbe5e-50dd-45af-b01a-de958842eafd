---
// src/layouts/Layout.astro
import Header from "@/components/layout/Header.astro";
import Footer from "@/components/layout/Footer.astro";
import IntroScreen from "@/components/intro/IntroScreen.astro";
import "@/styles/global.css";

// 1. Déclarer les props attendues (title et lang)
export interface Props {
  title: string;
  lang: "fr" | "en";
}
const { title, lang } = Astro.props;
const isHome = /^\/(fr|en)\/?$/.test(Astro.url.pathname);
---

<html lang={lang}>
  <head>
    <meta charset="UTF-8" />
    <title>{title}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Description de la page qui apparaîtra sur Google."
    />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
  </head>

  <body>
    {isHome && <IntroScreen />}

    <Header />

    <main>
      <slot />
    </main>

    <Footer />
  </body>
</html>
